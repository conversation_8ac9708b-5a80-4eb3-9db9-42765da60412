const CodemodFixer = require('./src/build/CodemodFixer');
const fs = require('fs-extra');
const path = require('path');

async function testWebpackCodemod() {
  console.log('🧪 测试 Webpack Codemod 功能...\n');

  const testDir = path.join(__dirname, 'test-project');
  const codemodFixer = new CodemodFixer(testDir, { verbose: true, dryRun: false });

  // 模拟包含 webpack 配置问题的构建输出
  const buildOutput = `
ERROR in webpack configuration
target function configuration error
output.library configuration error
  `;

  console.log('📋 模拟构建错误输出:');
  console.log(buildOutput);
  console.log('\n🔧 开始应用 webpack codemod 修复...\n');

  try {
    const result = await codemodFixer.applyCodemods(buildOutput);
    
    console.log('\n📊 修复结果:');
    console.log(`- 修改文件数: ${result.filesModified}`);
    
    const stats = codemodFixer.getStats();
    console.log(`- 处理文件数: ${stats.filesProcessed}`);
    console.log(`- 应用转换数: ${stats.transformsApplied}`);

    // 检查修复后的 webpack 配置文件
    const webpackConfig = path.join(testDir, 'webpack.config.js');
    if (await fs.pathExists(webpackConfig)) {
      console.log('\n📝 修复后的 Webpack 配置:');
      const content = await fs.readFile(webpackConfig, 'utf8');
      console.log(content);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testWebpackCodemod().catch(console.error);
