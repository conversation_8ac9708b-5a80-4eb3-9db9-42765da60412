const fs = require('fs-extra');
const path = require('path');
const CodemodFixer = require('../src/build/CodemodFixer');

describe('CodemodFixer', () => {
  let tempDir;
  let codemodFixer;

  beforeEach(async () => {
    tempDir = path.join(__dirname, 'temp', 'codemod-test');
    await fs.ensureDir(tempDir);
    codemodFixer = new CodemodFixer(tempDir, { verbose: false, dryRun: false });
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('JSON imports to default imports', () => {
    test('should convert named imports from JSON to default imports', async () => {
      const testFile = path.join(tempDir, 'test.js');
      const sourceCode = `
import { version, name } from './package.json';
import { config } from './config.json';

console.log(version, name);
console.log(config);
`;

      await fs.writeFile(testFile, sourceCode);

      const buildOutput = 'named exports from JSON modules';
      const result = await codemodFixer.applyCodemods(buildOutput);

      expect(result.filesModified).toBe(1);

      const transformedCode = await fs.readFile(testFile, 'utf8');
      expect(transformedCode).toContain('import pkg from \'./package.json\'');
      expect(transformedCode).toContain('import config from \'./config.json\'');
      expect(transformedCode).toContain('pkg.version');
      expect(transformedCode).toContain('pkg.name');
      expect(transformedCode).toContain('config.config');
    });

    test('should handle package.json imports specially', async () => {
      const testFile = path.join(tempDir, 'test.js');
      const sourceCode = `
import { version } from './package.json';
console.log(version);
`;

      await fs.writeFile(testFile, sourceCode);

      const buildOutput = 'named exports from JSON modules';
      const result = await codemodFixer.applyCodemods(buildOutput);

      expect(result.filesModified).toBe(1);

      const transformedCode = await fs.readFile(testFile, 'utf8');
      expect(transformedCode).toContain('import pkg from \'./package.json\'');
      expect(transformedCode).toContain('pkg.version');
    });

    test('should preserve quote style', async () => {
      const testFile = path.join(tempDir, 'test.js');
      const sourceCode = `
import { version } from "./package.json";
console.log(version);
`;

      await fs.writeFile(testFile, sourceCode);

      const buildOutput = 'named exports from JSON modules';
      await codemodFixer.applyCodemods(buildOutput);

      const transformedCode = await fs.readFile(testFile, 'utf8');
      expect(transformedCode).toContain('import pkg from "./package.json"');
    });
  });

  describe('Webpack v5 target configuration', () => {
    test('should convert target function to target false with plugins', async () => {
      const testFile = path.join(tempDir, 'webpack.config.js');
      const sourceCode = `
module.exports = {
  target: WebExtensionTarget(nodeConfig),
  entry: './src/index.js'
};
`;

      await fs.writeFile(testFile, sourceCode);

      const buildOutput = 'target function configuration error';
      const result = await codemodFixer.applyCodemods(buildOutput);

      expect(result.filesModified).toBe(1);

      const transformedCode = await fs.readFile(testFile, 'utf8');
      expect(transformedCode).toContain('target: false');
      expect(transformedCode).toContain('plugins: [');
      expect(transformedCode).toContain('WebExtensionTarget(nodeConfig)');
    });
  });

  describe('Library configuration', () => {
    test('should convert library and libraryTarget to library object', async () => {
      const testFile = path.join(tempDir, 'webpack.config.js');
      const sourceCode = `
module.exports = {
  output: {
    library: 'MyLibrary',
    libraryTarget: 'commonjs2',
    filename: 'bundle.js'
  }
};
`;

      await fs.writeFile(testFile, sourceCode);

      const buildOutput = 'output.library configuration error';
      const result = await codemodFixer.applyCodemods(buildOutput);

      expect(result.filesModified).toBe(1);

      const transformedCode = await fs.readFile(testFile, 'utf8');
      expect(transformedCode).toContain('library: {');
      expect(transformedCode).toContain('name: \'MyLibrary\'');
      expect(transformedCode).toContain('type: \'commonjs2\'');
      expect(transformedCode).not.toContain('libraryTarget:');
    });
  });

  describe('Detection logic', () => {
    test('should detect JSON import issues', () => {
      const buildOutput = 'named exports from JSON modules are not supported';
      const fixes = codemodFixer.detectRequiredFixes(buildOutput);
      
      expect(fixes).toHaveLength(1);
      expect(fixes[0].name).toBe('json-imports-to-default-imports');
    });

    test('should detect webpack target issues', () => {
      const buildOutput = 'target function configuration error';
      const fixes = codemodFixer.detectRequiredFixes(buildOutput);
      
      expect(fixes).toHaveLength(1);
      expect(fixes[0].name).toBe('webpack-v5-target-false');
    });

    test('should detect library configuration issues', () => {
      const buildOutput = 'output.library configuration error';
      const fixes = codemodFixer.detectRequiredFixes(buildOutput);
      
      expect(fixes).toHaveLength(1);
      expect(fixes[0].name).toBe('library-target-to-library-object');
    });

    test('should return empty array when no issues detected', () => {
      const buildOutput = 'some other error that we cannot fix';
      const fixes = codemodFixer.detectRequiredFixes(buildOutput);
      
      expect(fixes).toHaveLength(0);
    });
  });

  describe('File finding', () => {
    test('should find files matching patterns', async () => {
      await fs.writeFile(path.join(tempDir, 'test.js'), 'console.log("test");');
      await fs.writeFile(path.join(tempDir, 'test.ts'), 'console.log("test");');
      await fs.ensureDir(path.join(tempDir, 'src'));
      await fs.writeFile(path.join(tempDir, 'src', 'app.vue'), '<template></template>');

      const files = await codemodFixer.findFiles(['**/*.js', '**/*.ts', '**/*.vue']);
      
      expect(files).toHaveLength(3);
      expect(files.some(f => f.endsWith('test.js'))).toBe(true);
      expect(files.some(f => f.endsWith('test.ts'))).toBe(true);
      expect(files.some(f => f.endsWith('app.vue'))).toBe(true);
    });

    test('should ignore node_modules and dist directories', async () => {
      await fs.ensureDir(path.join(tempDir, 'node_modules'));
      await fs.writeFile(path.join(tempDir, 'node_modules', 'test.js'), 'console.log("test");');
      await fs.ensureDir(path.join(tempDir, 'dist'));
      await fs.writeFile(path.join(tempDir, 'dist', 'test.js'), 'console.log("test");');
      await fs.writeFile(path.join(tempDir, 'test.js'), 'console.log("test");');

      const files = await codemodFixer.findFiles(['**/*.js']);
      
      expect(files).toHaveLength(1);
      expect(files[0]).toContain('test.js');
      expect(files[0]).not.toContain('node_modules');
      expect(files[0]).not.toContain('dist');
    });
  });

  describe('Statistics', () => {
    test('should track statistics correctly', async () => {
      const testFile = path.join(tempDir, 'test.js');
      const sourceCode = `
import { version } from './package.json';
console.log(version);
`;

      await fs.writeFile(testFile, sourceCode);

      const buildOutput = 'named exports from JSON modules';
      await codemodFixer.applyCodemods(buildOutput);

      const stats = codemodFixer.getStats();
      expect(stats.filesProcessed).toBe(1);
      expect(stats.filesModified).toBe(1);
      expect(stats.transformsApplied).toBe(1);
    });
  });
});
