const CodemodFixer = require('./src/build/CodemodFixer');
const fs = require('fs-extra');
const path = require('path');

async function testCodemodFixer() {
  console.log('🧪 测试 CodemodFixer 功能...\n');

  const testDir = path.join(__dirname, 'test-project');
  const codemodFixer = new CodemodFixer(testDir, { verbose: true, dryRun: false });

  // 模拟包含 JSON 导入问题的构建输出
  const buildOutput = `
ERROR in ./src/test-json-imports.js
Module build failed: named exports from JSON modules are not supported
  import { version, name } from '../package.json';
         ^^^^^^^^^^^^^^^^
  `;

  console.log('📋 模拟构建错误输出:');
  console.log(buildOutput);
  console.log('\n🔧 开始应用 codemod 修复...\n');

  try {
    const result = await codemodFixer.applyCodemods(buildOutput);
    
    console.log('\n📊 修复结果:');
    console.log(`- 修改文件数: ${result.filesModified}`);
    
    const stats = codemodFixer.getStats();
    console.log(`- 处理文件数: ${stats.filesProcessed}`);
    console.log(`- 应用转换数: ${stats.transformsApplied}`);

    // 检查修复后的文件内容
    const fixedFile = path.join(testDir, 'src', 'test-json-imports.js');
    if (await fs.pathExists(fixedFile)) {
      console.log('\n📝 修复后的文件内容:');
      const content = await fs.readFile(fixedFile, 'utf8');
      console.log(content);
    }

    // 检查 webpack 配置文件
    const webpackConfig = path.join(testDir, 'webpack.config.js');
    if (await fs.pathExists(webpackConfig)) {
      console.log('\n📝 Webpack 配置文件:');
      const content = await fs.readFile(webpackConfig, 'utf8');
      console.log(content);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testCodemodFixer().catch(console.error);
