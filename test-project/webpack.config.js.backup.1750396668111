const path = require('path');

function WebExtensionTarget(nodeConfig) {
  return {
    // Mock webpack target function
    name: 'web-extension',
    config: nodeConfig
  };
}

const nodeConfig = { node: true };

module.exports = {
  entry: './src/main.js',
  target: false,
  plugins: [
    WebExtensionTarget(nodeConfig)
  ],
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js',
    library: 'MyLibrary',
    libraryTarget: 'commonjs2'
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      }
    ]
  }
};
