const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const jscodeshift = require('jscodeshift');

/**
 * Codemod 修复器
 * 基于 codemod 的兼容方案，用于支持没有 AI 的场景
 * 
 * 核心特性：
 * - 使用 jscodeshift 进行代码转换
 * - 支持 webpack v5 迁移 codemods
 * - 预定义的修复规则
 * - 无需 AI 服务
 */
class CodemodFixer {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      verbose: false,
      dryRun: false,
      ...options
    };

    // 内置的 codemod 转换规则
    this.codemods = {
      'json-imports-to-default-imports': this.jsonImportsToDefaultImports.bind(this),
      'webpack-v5-target-false': this.webpackV5TargetFalse.bind(this),
      'library-target-to-library-object': this.libraryTargetToLibraryObject.bind(this)
    };

    this.stats = {
      filesProcessed: 0,
      filesModified: 0,
      transformsApplied: 0
    };
  }

  /**
   * 应用 codemod 修复
   */
  async applyCodemods(buildOutput) {
    console.log(chalk.blue('🔧 开始应用 codemod 修复...'));

    const fixes = this.detectRequiredFixes(buildOutput);
    
    if (fixes.length === 0) {
      console.log(chalk.yellow('⚠️  未检测到可用的 codemod 修复'));
      return { filesModified: 0 };
    }

    let totalFilesModified = 0;

    for (const fix of fixes) {
      console.log(chalk.gray(`  应用修复: ${fix.name}`));
      
      try {
        const result = await this.applySingleCodemod(fix);
        totalFilesModified += result.filesModified;
        this.stats.transformsApplied++;
        
        if (result.filesModified > 0) {
          console.log(chalk.green(`    ✅ 修改了 ${result.filesModified} 个文件`));
        } else {
          console.log(chalk.gray(`    📝 没有文件需要修改`));
        }
      } catch (error) {
        console.log(chalk.red(`    ❌ 修复失败: ${error.message}`));
      }
    }

    console.log(chalk.blue(`🎯 Codemod 修复完成，共修改 ${totalFilesModified} 个文件`));
    return { filesModified: totalFilesModified };
  }

  /**
   * 检测需要的修复
   */
  detectRequiredFixes(buildOutput) {
    const fixes = [];

    // 检测 JSON 导入问题
    if (buildOutput.includes('named exports from JSON modules') ||
        buildOutput.includes('import { version } from')) {
      fixes.push({
        name: 'json-imports-to-default-imports',
        description: '修复 JSON 模块的命名导入',
        patterns: ['**/*.js', '**/*.ts', '**/*.vue']
      });
    }

    // 检测 webpack target 问题
    if (buildOutput.includes('target') && buildOutput.includes('function')) {
      fixes.push({
        name: 'webpack-v5-target-false',
        description: '修复 webpack v5 target 配置',
        patterns: ['webpack.config.js', 'vue.config.js']
      });
    }

    // 检测 library 配置问题
    if (buildOutput.includes('output.library') || buildOutput.includes('libraryTarget')) {
      fixes.push({
        name: 'library-target-to-library-object',
        description: '修复 webpack v5 library 配置',
        patterns: ['webpack.config.js', 'vue.config.js']
      });
    }

    return fixes;
  }

  /**
   * 应用单个 codemod
   */
  async applySingleCodemod(fix) {
    const codemodFn = this.codemods[fix.name];
    if (!codemodFn) {
      throw new Error(`未找到 codemod: ${fix.name}`);
    }

    const files = await this.findFiles(fix.patterns);
    let filesModified = 0;

    for (const filePath of files) {
      try {
        const source = await fs.readFile(filePath, 'utf8');
        const result = codemodFn(source, filePath);

        if (result && result !== source) {
          if (!this.options.dryRun) {
            await this.writeFileWithBackup(filePath, result);
          }
          filesModified++;
          this.stats.filesModified++;
        }
        this.stats.filesProcessed++;
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  处理文件失败: ${path.relative(this.projectPath, filePath)} - ${error.message}`));
        }
      }
    }

    return { filesModified };
  }

  /**
   * JSON 导入转换为默认导入
   * 基于 webpack/v5/json-imports-to-default-imports codemod
   */
  jsonImportsToDefaultImports(source, filePath) {
    const j = jscodeshift.withParser('tsx');
    const root = j(source);
    let dirtyFlag = false;

    // 确定原始引号风格
    const originalQuoteStyle = source.includes("'") ? 'single' : 'double';

    // 查找所有导入声明
    root.find(j.ImportDeclaration).forEach((path) => {
      const importPath = path.node.source.value;

      // 检查是否从 JSON 文件导入
      if (importPath.endsWith('.json')) {
        const specifiers = path.node.specifiers;

        // 检查是否有命名导入
        if (specifiers.some((specifier) => j.ImportSpecifier.check(specifier))) {
          // 确定默认导入标识符
          const importBaseName = importPath.split('/').pop().replace('.json', '');
          const defaultImportName = importBaseName === 'package' ? 'pkg' : importBaseName;
          const defaultImportIdentifier = j.identifier(defaultImportName);

          // 创建新的默认导入声明
          const newImportDeclaration = j.importDeclaration(
            [j.importDefaultSpecifier(defaultImportIdentifier)],
            j.literal(importPath)
          );

          // 替换旧的导入声明
          j(path).replaceWith(newImportDeclaration);

          // 替换所有对命名导入的引用
          specifiers.forEach((specifier) => {
            if (j.ImportSpecifier.check(specifier)) {
              const localName = specifier.local.name;
              const importedName = specifier.imported.name;

              root
                .find(j.Identifier, { name: localName })
                .filter((idPath) => {
                  const parent = idPath.parent.node;
                  return (
                    !j.ImportDeclaration.check(parent) &&
                    !j.ImportSpecifier.check(parent) &&
                    !j.ImportDefaultSpecifier.check(parent) &&
                    !j.ImportNamespaceSpecifier.check(parent)
                  );
                })
                .forEach((identifierPath) => {
                  j(identifierPath).replaceWith(
                    j.memberExpression(
                      defaultImportIdentifier,
                      j.identifier(importedName)
                    )
                  );
                });
            }
          });

          dirtyFlag = true;
        }
      }
    });

    return dirtyFlag ? root.toSource({ quote: originalQuoteStyle }) : undefined;
  }

  /**
   * Webpack v5 target 配置修复
   */
  webpackV5TargetFalse(source, filePath) {
    if (!filePath.includes('webpack.config.js') && !filePath.includes('vue.config.js')) {
      return undefined;
    }

    let modified = false;
    let result = source;

    // 修复 target 函数配置
    const targetFunctionRegex = /target:\s*([A-Za-z_$][A-Za-z0-9_$]*)\s*\(\s*([^)]*)\s*\)/g;
    result = result.replace(targetFunctionRegex, (match, funcName, args) => {
      modified = true;
      return `target: false,\n  plugins: [\n    ${funcName}(${args})\n  ]`;
    });

    return modified ? result : undefined;
  }

  /**
   * Library target 配置修复
   */
  libraryTargetToLibraryObject(source, filePath) {
    if (!filePath.includes('webpack.config.js') && !filePath.includes('vue.config.js')) {
      return undefined;
    }

    let modified = false;
    let result = source;

    // 修复 output.library 和 output.libraryTarget
    const libraryConfigRegex = /output:\s*{([^}]*libraryTarget[^}]*)}/gs;
    result = result.replace(libraryConfigRegex, (match, content) => {
      let newContent = content;
      
      // 提取 library 和 libraryTarget 值
      const libraryMatch = newContent.match(/library:\s*['"]([^'"]+)['"]/);
      const libraryTargetMatch = newContent.match(/libraryTarget:\s*['"]([^'"]+)['"]/);
      
      if (libraryMatch && libraryTargetMatch) {
        const libraryName = libraryMatch[1];
        const libraryType = libraryTargetMatch[1];
        
        // 移除旧的配置
        newContent = newContent.replace(/library:\s*['"][^'"]+['"],?\s*/g, '');
        newContent = newContent.replace(/libraryTarget:\s*['"][^'"]+['"],?\s*/g, '');
        
        // 添加新的配置
        newContent += `\n    library: {\n      name: '${libraryName}',\n      type: '${libraryType}'\n    },`;
        
        modified = true;
      }
      
      return `output: {${newContent}}`;
    });

    return modified ? result : undefined;
  }

  /**
   * 查找匹配的文件
   */
  async findFiles(patterns) {
    const glob = require('glob');
    const files = new Set();

    for (const pattern of patterns) {
      const matches = glob.sync(pattern, {
        cwd: this.projectPath,
        absolute: true,
        ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
      });
      
      matches.forEach(file => files.add(file));
    }

    return Array.from(files);
  }

  /**
   * 写入文件并备份
   */
  async writeFileWithBackup(filePath, content) {
    // 备份原文件
    if (await fs.pathExists(filePath)) {
      const backupPath = `${filePath}.backup.${Date.now()}`;
      await fs.copy(filePath, backupPath);
    }

    await fs.writeFile(filePath, content, 'utf8');
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

module.exports = CodemodFixer;
